import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsInt,
  IsOptional,
  IsEnum,
  IsDate,
  MaxLength,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { EmploymentType } from '../enum/employment-type.enum';
import { MaritalStatus } from '../enum/marital-status.enum';

/**
 * DTO for creating a new employee
 */
export class CreateEmployeeDto {
  /**
   * Employee code (unique identifier in HR context)
   * @example "EMP001"
   */
  @ApiProperty({
    description: 'Employee code (unique identifier in HR context)',
    example: 'EMP001',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  employeeCode: string;

  /**
   * Employee name
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({ description: 'Employee name', example: '<PERSON><PERSON><PERSON><PERSON>' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  employeeName: string;

  /**
   * ID of the department the employee belongs to
   * @example 1
   */
  @ApiProperty({
    required: false,
    description: 'ID of the department the employee belongs to',
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  departmentId?: number;

  /**
   * Employee's job title
   * @example "Software Engineer"
   */
  @ApiProperty({
    required: false,
    description: "Employee's job title",
    example: 'Software Engineer',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  jobTitle?: string;

  /**
   * Employee's job level
   * @example "Senior"
   */
  @ApiProperty({
    required: false,
    description: "Employee's job level",
    example: 'Senior',
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  jobLevel?: string;

  /**
   * Employee's manager's ID
   * @example 2
   */
  @ApiProperty({
    required: false,
    description: "Employee's manager's ID",
    example: 2,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  managerId?: number;

  /**
   * Employee's employment type
   * @example "full_time"
   */
  @ApiProperty({
    required: false,
    description: "Employee's employment type",
    enum: EmploymentType,
    example: EmploymentType.FULL_TIME,
  })
  @IsOptional()
  @IsEnum(EmploymentType)
  employmentType?: EmploymentType;

  /**
   * Employee's status
   * @example "active"
   */
  @ApiProperty({
    required: false,
    description: "Employee's status",
    enum: EmployeeStatus,
    default: EmployeeStatus.ACTIVE,
    example: EmployeeStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EmployeeStatus)
  status?: EmployeeStatus;

  /**
   * Employee's hire date
   * @example "2023-01-15"
   */
  @ApiProperty({
    required: false,
    description: "Employee's hire date",
    example: '2023-01-15',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  hireDate?: Date;

  /**
   * Employee's probation end date
   * @example "2023-04-15"
   */
  @ApiProperty({
    required: false,
    description: "Employee's probation end date",
    example: '2023-04-15',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  probationEndDate?: Date;

  /**
   * Employee's marital status
   * @example "single"
   */
  @ApiProperty({
    required: false,
    description: "Employee's marital status",
    enum: MaritalStatus,
    example: MaritalStatus.SINGLE,
  })
  @IsOptional()
  @IsEnum(MaritalStatus)
  maritalStatus?: MaritalStatus;

  /**
   * Number of dependents
   * @example 0
   */
  @ApiProperty({
    required: false,
    description: 'Number of dependents',
    example: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  numberOfDependents?: number;

  /**
   * Emergency contact name
   * @example "John Doe"
   */
  @ApiProperty({
    required: false,
    description: 'Emergency contact name',
    example: 'John Doe',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  emergencyContactName?: string;

  /**
   * Emergency contact phone
   * @example "0123456789"
   */
  @ApiProperty({
    required: false,
    description: 'Emergency contact phone',
    example: '0123456789',
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  emergencyContactPhone?: string;

  /**
   * Emergency contact relationship
   * @example "Parent"
   */
  @ApiProperty({
    required: false,
    description: 'Emergency contact relationship',
    example: 'Parent',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  emergencyContactRelationship?: string;

  /**
   * Notes about the employee
   * @example "Excellent team player"
   */
  @ApiProperty({
    required: false,
    description: 'Notes about the employee',
    example: 'Excellent team player',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
