import { NestFactory } from '@nestjs/core';
import { AppModule } from '@app';
import { SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe, VersioningType, Logger, BadRequestException } from '@nestjs/common';
import { createSwaggerConfig, swaggerCustomOptions } from '@common/swagger';
import { ConfigService } from '@nestjs/config';
import * as dotenv from 'dotenv';
import { initializeTypeOrmTransactional } from '@config/typeorm-transactional.config';
import { corsConfig } from '@common/filters/cors.config';

// Khởi tạo typeorm-transactional
initializeTypeOrmTransactional();

dotenv.config();

async function bootstrap() {
  // Enable debug logging cho tenant system nếu ở development mode
  if (
    process.env.NODE_ENV === 'development' ||
    process.env.ENABLE_TENANT_DEBUG === 'true'
  ) {
    Logger.overrideLogger(['log', 'debug', 'error', 'verbose', 'warn']);
  }

  const app = await NestFactory.create(AppModule, {
    logger:
      process.env.NODE_ENV === 'development'
        ? ['log', 'debug', 'error', 'verbose', 'warn']
        : ['log', 'error', 'warn'],
  });

  // Thêm global prefix với version
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  // Cấu hình validation pipe với error messages chi tiết
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: false,
      validationError: {
        target: false,
        value: false,
      },
      exceptionFactory: (errors) => {
        const result = {};
        errors.forEach((error) => {
          result[error.property] = Object.values(error.constraints || {});
        });
        return new BadRequestException({
          message: 'Validation failed',
          errors: result,
        });
      },
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Cấu hình CORS với danh sách các frontend được phép
  app.enableCors(corsConfig);

  // Cấu hình Swagger với thông tin từ biến môi trường
  const configService = app.get(ConfigService);
  const swaggerConfig = createSwaggerConfig(configService);
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api/docs', app, document, swaggerCustomOptions);

  // Khởi động server
  const port = process.env.PORT ?? 3000;
  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
  console.log(
    `Swagger documentation is available at: http://localhost:${port}/api/docs`,
  );
}
bootstrap().catch((err) => {
  console.error(err);
  process.exit(1);
});
