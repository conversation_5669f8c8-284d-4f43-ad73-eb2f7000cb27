import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDateOfBirthAndGenderToEmployees1703000000002
  implements MigrationInterface
{
  name = 'AddDateOfBirthAndGenderToEmployees1703000000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo enum gender_enum nếu chưa tồn tại
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "gender_enum" AS ENUM('male', 'female', 'other');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Thêm cột date_of_birth nếu chưa tồn tại
    await queryRunner.query(`
      DO $$ BEGIN
        ALTER TABLE "employees" ADD COLUMN "date_of_birth" date;
      EXCEPTION
        WHEN duplicate_column THEN null;
      END $$;
    `);

    // Thêm cột gender nếu chưa tồn tại
    await queryRunner.query(`
      DO $$ BEGIN
        ALTER TABLE "employees" ADD COLUMN "gender" "gender_enum";
      EXCEPTION
        WHEN duplicate_column THEN null;
      END $$;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột gender
    await queryRunner.query(`
      ALTER TABLE "employees" DROP COLUMN IF EXISTS "gender"
    `);

    // Xóa cột date_of_birth
    await queryRunner.query(`
      ALTER TABLE "employees" DROP COLUMN IF EXISTS "date_of_birth"
    `);

    // Xóa enum gender_enum (chỉ nếu không còn được sử dụng)
    await queryRunner.query(`
      DROP TYPE IF EXISTS "gender_enum"
    `);
  }
}
