import { DataSource } from 'typeorm';
import { config } from 'dotenv';

// Load biến môi trường từ file .env
config();

/**
 * <PERSON><PERSON><PERSON> hình kết nối database
 */
export const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_DATABASE || 'ai_erp',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: false,
});
