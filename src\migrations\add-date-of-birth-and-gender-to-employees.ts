import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDateOfBirthAndGenderToEmployees1703000000002
  implements MigrationInterface
{
  name = 'AddDateOfBirthAndGenderToEmployees1703000000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo enum gender_enum
    await queryRunner.query(`
      CREATE TYPE "gender_enum" AS ENUM('male', 'female', 'other')
    `);

    // Thêm cột date_of_birth
    await queryRunner.query(`
      ALTER TABLE "employees" 
      ADD COLUMN "date_of_birth" date
    `);

    // Thêm cột gender
    await queryRunner.query(`
      ALTER TABLE "employees" 
      ADD COLUMN "gender" "gender_enum"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột gender
    await queryRunner.query(`
      ALTER TABLE "employees" 
      DROP COLUMN "gender"
    `);

    // Xóa cột date_of_birth
    await queryRunner.query(`
      ALTER TABLE "employees" 
      DROP COLUMN "date_of_birth"
    `);

    // Xóa enum gender_enum
    await queryRunner.query(`
      DROP TYPE "gender_enum"
    `);
  }
}
