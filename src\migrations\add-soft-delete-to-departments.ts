import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSoftDeleteToDepartments1703000000000
  implements MigrationInterface
{
  name = 'AddSoftDeleteToDepartments1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột is_active với giá trị mặc định là true
    await queryRunner.addColumn(
      'departments',
      new TableColumn({
        name: 'is_active',
        type: 'boolean',
        default: true,
        isNullable: false,
      }),
    );

    // Thêm cột deleted_at để lưu thời điểm xóa
    await queryRunner.addColumn(
      'departments',
      new TableColumn({
        name: 'deleted_at',
        type: 'bigint',
        isNullable: true,
      }),
    );

    // Cập nhật tất cả các bản ghi hiện tại để có is_active = true
    await queryRunner.query(`
      UPDATE departments 
      SET is_active = true 
      WHERE is_active IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột deleted_at
    await queryRunner.dropColumn('departments', 'deleted_at');

    // Xóa cột is_active
    await queryRunner.dropColumn('departments', 'is_active');
  }
}
