import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { QueryFailedError, EntityNotFoundError } from 'typeorm';
import { ValidationError } from 'class-validator';
import { v4 as uuidv4 } from 'uuid';
import { AppException, ErrorCode } from '@/common';
import { ErrorResponse } from '@common/exceptions/error-res.dto';

/**
 * Filter bắt tất cả các exception trong ứng dụng
 * Chuyển đổi các exception thành response chuẩn
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const path = request.url;
    const requestId = (request.headers['x-request-id'] as string) || uuidv4();

    let errorResponse: ErrorResponse;
    let status: HttpStatus;

    // Xử lý AppException (custom exception)
    if (exception instanceof AppException) {
      const exceptionResponse = exception.getResponse() as ErrorResponse;
      errorResponse = {
        ...exceptionResponse,
        path,
        requestId,
      };
      status = exception.getStatus();
    }
    // Xử lý đặc biệt cho BadRequestException từ ValidationPipe
    else if (exception instanceof BadRequestException) {
      const exceptionResponse = exception.getResponse();

      // Log chi tiết lỗi validation để debug
      this.logger.debug(
        'BadRequestException details:',
        JSON.stringify(exceptionResponse, null, 2),
      );

      // Kiểm tra nếu có errors từ custom exceptionFactory
      if (
        typeof exceptionResponse === 'object' &&
        (exceptionResponse as any).errors
      ) {
        errorResponse = {
          code: ErrorCode.VALIDATION_ERROR,
          message: 'Validation failed',
          details: (exceptionResponse as any).errors,
          timestamp: new Date().toISOString(),
          path,
          requestId,
        };
      } else {
        // Fallback cho BadRequestException khác
        errorResponse = {
          code: ErrorCode.VALIDATION_ERROR,
          message: 'Bad request',
          details:
            typeof exceptionResponse === 'string'
              ? { general: [exceptionResponse] }
              : exceptionResponse,
          timestamp: new Date().toISOString(),
          path,
          requestId,
        };
      }

      status = HttpStatus.BAD_REQUEST;
    }
    // Xử lý HttpException từ NestJS
    else if (exception instanceof HttpException) {
      const exceptionResponse = exception.getResponse();

      // Xử lý đặc biệt cho BadRequestException từ ValidationPipe (fallback)
      if (
        exception.getStatus() === HttpStatus.BAD_REQUEST &&
        typeof exceptionResponse === 'object' &&
        (exceptionResponse as any).message
      ) {
        // Log chi tiết lỗi validation để debug
        this.logger.debug(
          'Validation error details:',
          JSON.stringify(exceptionResponse, null, 2),
        );
        this.logger.debug(
          'Exception message:',
          exception.message,
        );
        this.logger.debug(
          'Has errors property:',
          !!(exceptionResponse as any).errors,
        );

        // Kiểm tra nếu có errors từ custom exceptionFactory
        if ((exceptionResponse as any).errors) {
          errorResponse = {
            code: ErrorCode.VALIDATION_ERROR,
            message: 'Validation failed',
            details: (exceptionResponse as any).errors,
            timestamp: new Date().toISOString(),
            path,
            requestId,
          };
        } else {
          // Xử lý trường hợp cũ với message array
          const validationErrors = {};

          // Xử lý trường hợp message là mảng
          if (Array.isArray((exceptionResponse as any).message)) {
            for (const error of (exceptionResponse as any).message) {
              if (typeof error === 'string' && error.includes(' - ')) {
                const [property, message] = error.split(' - ');
                if (!validationErrors[property]) {
                  validationErrors[property] = [];
                }
                validationErrors[property].push(message);
              } else {
                // Nếu không phải dạng chuẩn, giữ nguyên lỗi
                if (!validationErrors['general']) {
                  validationErrors['general'] = [];
                }
                validationErrors['general'].push(error);
              }
            }
          }
          // Xử lý trường hợp message là chuỗi
          else if (typeof (exceptionResponse as any).message === 'string') {
            validationErrors['general'] = [(exceptionResponse as any).message];
          }
          // Xử lý trường hợp message là object
          else {
            validationErrors['general'] = ['Validation failed'];
            validationErrors['details'] = (exceptionResponse as any).message;
          }

          errorResponse = {
            code: ErrorCode.VALIDATION_ERROR,
            message: 'Validation failed',
            details: validationErrors,
            timestamp: new Date().toISOString(),
            path,
            requestId,
          };
        }
      } else {
        // Xử lý các HttpException khác
        errorResponse = {
          code: this.mapHttpStatusToErrorCode(exception.getStatus()),
          message:
            typeof exceptionResponse === 'string'
              ? exceptionResponse
              : (exceptionResponse as any).message || 'Http Exception',
          details:
            typeof exceptionResponse === 'object'
              ? (exceptionResponse as any).details ||
                (exceptionResponse as any).error
              : undefined,
          timestamp: new Date().toISOString(),
          path,
          requestId,
        };
      }

      status = exception.getStatus();
    }
    // Xử lý lỗi TypeORM
    else if (exception instanceof QueryFailedError) {
      errorResponse = {
        code: ErrorCode.DATABASE_ERROR,
        message: 'Database query failed',
        details: this.sanitizeDatabaseError(exception),
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };
      status = HttpStatus.BAD_REQUEST;
    }
    // Xử lý lỗi Entity Not Found của TypeORM
    else if (exception instanceof EntityNotFoundError) {
      errorResponse = {
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: 'Entity not found',
        details: exception.message,
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };
      status = HttpStatus.NOT_FOUND;
    }
    // Xử lý lỗi validation từ class-validator
    else if (
      exception instanceof ValidationError ||
      (Array.isArray(exception) && exception[0] instanceof ValidationError)
    ) {
      const validationErrors = Array.isArray(exception)
        ? exception
        : [exception];

      errorResponse = {
        code: ErrorCode.VALIDATION_ERROR,
        message: 'Validation failed',
        details: this.formatValidationErrors(validationErrors),
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };
      status = HttpStatus.BAD_REQUEST;
    }
    // Xử lý các lỗi không xác định
    else {
      const error = exception as Error;

      errorResponse = {
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        message: 'Internal server error',
        details:
          process.env.NODE_ENV === 'production'
            ? 'An unexpected error occurred'
            : {
                name: error.name,
                message: error.message,
                stack: error.stack,
              },
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };
      status = HttpStatus.INTERNAL_SERVER_ERROR;
    }

    // Log lỗi
    this.logException(exception, errorResponse, request);

    // Trả về response
    response.status(status).json(errorResponse);
  }

  /**
   * Ánh xạ HTTP status code sang ErrorCode
   */
  private mapHttpStatusToErrorCode(status: HttpStatus): ErrorCode {
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return ErrorCode.VALIDATION_ERROR;
      case HttpStatus.NOT_FOUND:
        return ErrorCode.RESOURCE_NOT_FOUND;
      case HttpStatus.TOO_MANY_REQUESTS:
        return ErrorCode.RATE_LIMIT_EXCEEDED;
      default:
        return ErrorCode.INTERNAL_SERVER_ERROR;
    }
  }

  /**
   * Format lỗi validation từ class-validator
   */
  private formatValidationErrors(
    errors: ValidationError[],
  ): Record<string, any> {
    return errors.reduce((acc, error) => {
      acc[error.property] = Object.values(error.constraints || {});
      if (error.children?.length) {
        acc[error.property] = {
          ...acc[error.property],
          ...this.formatValidationErrors(error.children),
        };
      }
      return acc;
    }, {});
  }

  /**
   * Xử lý lỗi database để tránh lộ thông tin nhạy cảm
   */
  private sanitizeDatabaseError(error: QueryFailedError): any {
    // Kiểm tra lỗi trùng lặp (unique constraint)
    if (
      error.message.includes('duplicate key') ||
      error.message.includes('unique constraint')
    ) {
      return {
        type: 'UniqueConstraintViolation',
        message: 'A record with the same unique identifier already exists',
      };
    }

    // Kiểm tra lỗi foreign key
    if (error.message.includes('foreign key constraint')) {
      return {
        type: 'ForeignKeyConstraintViolation',
        message: 'Referenced record does not exist or cannot be modified',
      };
    }

    // Trả về thông tin lỗi chung nếu ở môi trường production
    if (process.env.NODE_ENV === 'production') {
      return {
        type: 'DatabaseError',
        message: 'A database error occurred',
      };
    }

    // Trả về thông tin chi tiết hơn trong môi trường development
    return {
      type: 'DatabaseError',
      message: error.message,
      query: (error as any).query,
      parameters: (error as any).parameters,
    };
  }

  /**
   * Log exception
   */
  private logException(
    exception: unknown,
    errorResponse: ErrorResponse,
    request: Request,
  ): void {
    const { method, url, body, headers, query } = request;

    // Loại bỏ các thông tin nhạy cảm từ body và headers
    const sanitizedBody = this.sanitizeSensitiveData(body);
    const sanitizedHeaders = this.sanitizeSensitiveData(headers, [
      'authorization',
      'cookie',
    ]);

    const logContext = {
      error: errorResponse,
      request: {
        method,
        url,
        query,
        body: sanitizedBody,
        headers: sanitizedHeaders,
        requestId: errorResponse.requestId,
      },
    };

    // Log theo mức độ nghiêm trọng
    if (errorResponse.code === ErrorCode.INTERNAL_SERVER_ERROR) {
      this.logger.error(
        `Internal Server Error: ${errorResponse.message}`,
        exception instanceof Error ? exception.stack : undefined,
        JSON.stringify(logContext),
      );
    } else if (
      errorResponse.code === ErrorCode.DATABASE_ERROR ||
      errorResponse.code === ErrorCode.EXTERNAL_SERVICE_ERROR ||
      errorResponse.code === ErrorCode.OPENAI_API_ERROR ||
      errorResponse.code === ErrorCode.CLOUD_FLARE_ERROR_UPLOAD
    ) {
      this.logger.error(
        `Service Error: ${errorResponse.message}`,
        JSON.stringify(logContext),
      );
    } else {
      this.logger.warn(
        `Client Error: ${errorResponse.message}`,
        JSON.stringify(logContext),
      );
    }
  }

  /**
   * Loại bỏ thông tin nhạy cảm từ dữ liệu
   */
  private sanitizeSensitiveData(
    data: any,
    sensitiveFields: string[] = [
      'password',
      'token',
      'secret',
      'key',
      'apiKey',
    ],
  ): any {
    if (!data) return data;

    const result = { ...data };

    for (const field of sensitiveFields) {
      if (result[field]) {
        result[field] = '***REDACTED***';
      }
    }

    return result;
  }
}
