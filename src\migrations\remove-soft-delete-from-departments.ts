import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveSoftDeleteFromDepartments1703000000001
  implements MigrationInterface
{
  name = 'RemoveSoftDeleteFromDepartments1703000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột deleted_at
    await queryRunner.dropColumn('departments', 'deleted_at');

    // Xóa cột is_active
    await queryRunner.dropColumn('departments', 'is_active');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Thêm lại cột is_active với giá trị mặc định là true
    await queryRunner.query(`
      ALTER TABLE "departments" 
      ADD COLUMN "is_active" boolean NOT NULL DEFAULT true
    `);

    // Thêm lại cột deleted_at
    await queryRunner.query(`
      ALTER TABLE "departments" 
      ADD COLUMN "deleted_at" bigint
    `);
  }
}
